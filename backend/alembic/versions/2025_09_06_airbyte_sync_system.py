"""Add Airbyte sync system tables

Revision ID: airbyte_sync_001
Revises: be8d54430939
Create Date: 2025-09-06 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'airbyte_sync_001'
down_revision = 'be8d54430939'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Upgrade to Airbyte sync system."""
    
    # Create sync_checkpoints table
    op.create_table('sync_checkpoints',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('shop_id', sa.Integer(), nullable=False),
        sa.Column('entity_name', sa.String(50), nullable=False),
        sa.Column('last_updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('last_id', sa.Text(), nullable=True),
        sa.Column('last_cursor', sa.Text(), nullable=True),
        sa.Column('airbyte_state', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('airbyte_stream_state', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('total_records', sa.Integer(), nullable=True, default=0),
        sa.Column('last_sync_duration_seconds', sa.Float(), nullable=True),
        sa.Column('last_sync_status', sa.String(20), nullable=True, default='pending'),
        sa.Column('last_error_message', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('last_successful_sync_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['shop_id'], ['stores.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('shop_id', 'entity_name', name='uq_sync_checkpoints_shop_entity')
    )
    
    # Create indexes for sync_checkpoints
    op.create_index('idx_sync_checkpoints_shop_entity', 'sync_checkpoints', ['shop_id', 'entity_name'])
    op.create_index('idx_sync_checkpoints_updated_at', 'sync_checkpoints', ['last_updated_at'])
    op.create_index('idx_sync_checkpoints_status', 'sync_checkpoints', ['last_sync_status'])
    
    # Create sync_jobs table
    op.create_table('sync_jobs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('shop_id', sa.Integer(), nullable=False),
        sa.Column('entity', sa.String(50), nullable=False),
        sa.Column('status', sa.String(20), nullable=False, default='pending'),
        sa.Column('sync_mode', sa.String(20), nullable=True, default='incremental'),
        sa.Column('triggered_by', sa.String(50), nullable=True),
        sa.Column('airbyte_job_id', sa.BigInteger(), nullable=True),
        sa.Column('airbyte_connection_id', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('airbyte_sync_status', sa.String(20), nullable=True),
        sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('finished_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('records_processed', sa.Integer(), nullable=True, default=0),
        sa.Column('records_failed', sa.Integer(), nullable=True, default=0),
        sa.Column('attempt_count', sa.Integer(), nullable=True, default=0),
        sa.Column('max_retries', sa.Integer(), nullable=True, default=3),
        sa.Column('error', sa.Text(), nullable=True),
        sa.Column('error_details', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('job_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True, default=sa.text("'{}'::jsonb")),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['shop_id'], ['stores.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for sync_jobs
    op.create_index('idx_sync_jobs_shop_entity', 'sync_jobs', ['shop_id', 'entity'])
    op.create_index('idx_sync_jobs_status', 'sync_jobs', ['status'])
    op.create_index('idx_sync_jobs_airbyte_job', 'sync_jobs', ['airbyte_job_id'])
    op.create_index('idx_sync_jobs_created_at', 'sync_jobs', ['created_at'])
    
    # Create webhook_events table
    op.create_table('webhook_events',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('shop_id', sa.Integer(), nullable=True),
        sa.Column('topic', sa.String(100), nullable=False),
        sa.Column('payload', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('webhook_id', sa.String(255), nullable=True),
        sa.Column('event_id', sa.String(255), nullable=False),
        sa.Column('dedup_key', sa.String(64), nullable=False),
        sa.Column('status', sa.String(20), nullable=True, default='pending'),
        sa.Column('processed', sa.Boolean(), nullable=True, default=False),
        sa.Column('processing_attempts', sa.Integer(), nullable=True, default=0),
        sa.Column('max_processing_attempts', sa.Integer(), nullable=True, default=3),
        sa.Column('hmac_verified', sa.Boolean(), nullable=True, default=False),
        sa.Column('hmac_signature', sa.Text(), nullable=True),
        sa.Column('received_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('processed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('last_error', sa.Text(), nullable=True),
        sa.Column('error_details', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('triggered_sync_job_id', sa.Integer(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['shop_id'], ['stores.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['triggered_sync_job_id'], ['sync_jobs.id']),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for webhook_events
    op.create_index('idx_webhook_events_dedup', 'webhook_events', ['dedup_key'], unique=True)
    op.create_index('idx_webhook_events_shop_topic', 'webhook_events', ['shop_id', 'topic'])
    op.create_index('idx_webhook_events_status', 'webhook_events', ['status'])
    op.create_index('idx_webhook_events_received_at', 'webhook_events', ['received_at'])
    op.create_index('idx_webhook_events_event_id', 'webhook_events', ['event_id'])
    
    # Create dead_letter_queue table
    op.create_table('dead_letter_queue',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('origin', sa.String(50), nullable=False),
        sa.Column('payload', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('error', sa.Text(), nullable=False),
        sa.Column('source_id', sa.String(255), nullable=True),
        sa.Column('shop_id', sa.Integer(), nullable=True),
        sa.Column('entity_type', sa.String(50), nullable=True),
        sa.Column('retry_count', sa.Integer(), nullable=True, default=0),
        sa.Column('max_retries', sa.Integer(), nullable=True, default=3),
        sa.Column('last_retry_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('resolved', sa.Boolean(), nullable=True, default=False),
        sa.Column('resolved_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('resolved_by', sa.String(255), nullable=True),
        sa.Column('resolution_notes', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.ForeignKeyConstraint(['shop_id'], ['stores.id'], ondelete='SET NULL'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes for dead_letter_queue
    op.create_index('idx_dlq_origin', 'dead_letter_queue', ['origin'])
    op.create_index('idx_dlq_shop_id', 'dead_letter_queue', ['shop_id'])
    op.create_index('idx_dlq_resolved', 'dead_letter_queue', ['resolved'])
    op.create_index('idx_dlq_created_at', 'dead_letter_queue', ['created_at'])
    
    # Create airbyte_connections table
    op.create_table('airbyte_connections',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('shop_id', sa.Integer(), nullable=False),
        sa.Column('connection_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('source_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('destination_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('workspace_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('name', sa.String(255), nullable=False),
        sa.Column('status', sa.String(20), nullable=True, default='active'),
        sa.Column('schedule_type', sa.String(20), nullable=True, default='manual'),
        sa.Column('schedule_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('configured_streams', postgresql.JSONB(astext_type=sa.Text()), nullable=False, default=sa.text("'[]'::jsonb")),
        sa.Column('namespace_definition', sa.String(50), nullable=True, default='destination'),
        sa.Column('namespace_format', sa.String(255), nullable=True),
        sa.Column('prefix', sa.String(100), nullable=True),
        sa.Column('connection_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True, default=sa.text("'{}'::jsonb")),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('last_sync_at', sa.DateTime(timezone=True), nullable=True),
        sa.ForeignKeyConstraint(['shop_id'], ['stores.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('connection_id', name='uq_airbyte_connections_connection_id'),
        sa.UniqueConstraint('shop_id', name='uq_airbyte_connections_shop')
    )
    
    # Create indexes for airbyte_connections
    op.create_index('idx_airbyte_connections_status', 'airbyte_connections', ['status'])
    
    # Create staging tables for Airbyte destination
    op.create_table('staging_products',
        sa.Column('_airbyte_ab_id', postgresql.UUID(as_uuid=True), server_default=sa.text('gen_random_uuid()'), nullable=True),
        sa.Column('_airbyte_emitted_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('_airbyte_normalized_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('_airbyte_products_hashid', sa.Text(), nullable=True),
        sa.Column('id', sa.Text(), nullable=True),
        sa.Column('title', sa.Text(), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('handle', sa.Text(), nullable=True),
        sa.Column('product_type', sa.Text(), nullable=True),
        sa.Column('vendor', sa.Text(), nullable=True),
        sa.Column('status', sa.Text(), nullable=True),
        sa.Column('tags', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('published_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('images', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('variants', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('options', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('metafields', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('processed', sa.Boolean(), nullable=True, default=False),
        sa.Column('processed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True)
    )
    
    # Create staging_orders table
    op.create_table('staging_orders',
        sa.Column('_airbyte_ab_id', postgresql.UUID(as_uuid=True), server_default=sa.text('gen_random_uuid()'), nullable=True),
        sa.Column('_airbyte_emitted_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('_airbyte_normalized_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('_airbyte_orders_hashid', sa.Text(), nullable=True),
        sa.Column('id', sa.Text(), nullable=True),
        sa.Column('name', sa.Text(), nullable=True),
        sa.Column('email', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('total_price', sa.Numeric(10, 2), nullable=True),
        sa.Column('subtotal_price', sa.Numeric(10, 2), nullable=True),
        sa.Column('total_tax', sa.Numeric(10, 2), nullable=True),
        sa.Column('currency', sa.Text(), nullable=True),
        sa.Column('financial_status', sa.Text(), nullable=True),
        sa.Column('fulfillment_status', sa.Text(), nullable=True),
        sa.Column('line_items', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('customer', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('billing_address', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('shipping_address', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('processed', sa.Boolean(), nullable=True, default=False),
        sa.Column('processed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True)
    )
    
    # Create staging_customers table
    op.create_table('staging_customers',
        sa.Column('_airbyte_ab_id', postgresql.UUID(as_uuid=True), server_default=sa.text('gen_random_uuid()'), nullable=True),
        sa.Column('_airbyte_emitted_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('_airbyte_normalized_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('_airbyte_customers_hashid', sa.Text(), nullable=True),
        sa.Column('id', sa.Text(), nullable=True),
        sa.Column('email', sa.Text(), nullable=True),
        sa.Column('first_name', sa.Text(), nullable=True),
        sa.Column('last_name', sa.Text(), nullable=True),
        sa.Column('phone', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('accepts_marketing', sa.Boolean(), nullable=True),
        sa.Column('state', sa.Text(), nullable=True),
        sa.Column('addresses', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('default_address', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('tags', sa.Text(), nullable=True),
        sa.Column('processed', sa.Boolean(), nullable=True, default=False),
        sa.Column('processed_at', sa.DateTime(timezone=True), nullable=True),
        sa.Column('error_message', sa.Text(), nullable=True)
    )
    
    # Create indexes for staging tables
    op.create_index('idx_staging_products_id', 'staging_products', ['id'])
    op.create_index('idx_staging_products_processed', 'staging_products', ['processed'])
    op.create_index('idx_staging_orders_id', 'staging_orders', ['id'])
    op.create_index('idx_staging_orders_processed', 'staging_orders', ['processed'])
    op.create_index('idx_staging_customers_id', 'staging_customers', ['id'])
    op.create_index('idx_staging_customers_processed', 'staging_customers', ['processed'])


def downgrade() -> None:
    """Downgrade from Airbyte sync system."""
    
    # Drop staging tables
    op.drop_table('staging_customers')
    op.drop_table('staging_orders')
    op.drop_table('staging_products')
    
    # Drop main tables
    op.drop_table('airbyte_connections')
    op.drop_table('dead_letter_queue')
    op.drop_table('webhook_events')
    op.drop_table('sync_jobs')
    op.drop_table('sync_checkpoints')
