"""
Enhanced database models for robust Shopify sync system.
Builds upon existing models with comprehensive tracking and checkpointing.
"""

from sqlalchemy import Column, Integer, String, Float, Text, DateTime, ForeignKey, Boolean, JSON, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from enum import Enum

from core.db.database import Base


class SyncEntityType(str, Enum):
    """Supported entity types for synchronization."""
    PRODUCTS = "products"
    ORDERS = "orders"
    CUSTOMERS = "customers"
    COLLECTIONS = "collections"
    INVENTORY = "inventory"


class SyncJobStatus(str, Enum):
    """Sync job status enumeration."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"


class WebhookEventStatus(str, Enum):
    """Webhook event processing status."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"
    DUPLICATE = "duplicate"


class SyncCheckpoint(Base):
    """
    Tracks sync progress and checkpoints for incremental synchronization.
    Supports both cursor-based and timestamp-based checkpointing.
    """
    __tablename__ = "sync_checkpoints"

    id = Column(Integer, primary_key=True, index=True)
    store_id = Column(Integer, ForeignKey("stores.id"), nullable=False)
    entity_type = Column(String(50), nullable=False)  # products, orders, customers, etc.
    
    # Checkpoint data for resuming sync
    last_updated_at = Column(DateTime(timezone=True), nullable=True)
    last_synced_id = Column(String(255), nullable=True)  # Last processed entity ID
    last_end_cursor = Column(String(500), nullable=True)  # GraphQL cursor for pagination
    
    # Sync metadata
    total_entities = Column(Integer, default=0)
    last_sync_duration_seconds = Column(Float, nullable=True)
    last_sync_status = Column(String(20), default="pending")
    last_error_message = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_successful_sync_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    store = relationship("modules.stores.models.Store")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_sync_checkpoints_store_entity', 'store_id', 'entity_type'),
        Index('idx_sync_checkpoints_updated_at', 'last_updated_at'),
    )


class SyncJob(Base):
    """
    Tracks individual sync jobs with detailed progress and error handling.
    Enhanced version of existing SyncProgress model.
    """
    __tablename__ = "sync_jobs"

    id = Column(Integer, primary_key=True, index=True)
    store_id = Column(Integer, ForeignKey("stores.id"), nullable=False)
    entity_type = Column(String(50), nullable=False)
    job_type = Column(String(20), nullable=False)  # 'incremental', 'full', 'backfill'
    
    # Job configuration
    sync_mode = Column(String(20), default="incremental")  # incremental, full, bulk
    batch_size = Column(Integer, default=50)
    priority = Column(Integer, default=5)  # 1-10, higher = more important
    
    # Progress tracking
    status = Column(String(20), default=SyncJobStatus.PENDING.value)
    total_entities = Column(Integer, default=0)
    processed_entities = Column(Integer, default=0)
    successful_entities = Column(Integer, default=0)
    failed_entities = Column(Integer, default=0)
    current_page = Column(Integer, default=0)
    total_pages = Column(Integer, default=0)
    
    # Timing and performance
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    estimated_completion_at = Column(DateTime(timezone=True), nullable=True)
    duration_seconds = Column(Float, nullable=True)
    
    # Error handling
    retry_count = Column(Integer, default=0)
    max_retries = Column(Integer, default=3)
    last_error_message = Column(Text, nullable=True)
    error_details = Column(JSON, nullable=True)
    
    # Rate limiting and API usage
    api_calls_made = Column(Integer, default=0)
    rate_limit_hits = Column(Integer, default=0)
    cost_consumed = Column(Integer, default=0)  # GraphQL cost points
    
    # Celery task tracking
    celery_task_id = Column(String(255), nullable=True, index=True)
    
    # Metadata
    job_metadata = Column(JSON, nullable=True, default=dict)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    store = relationship("modules.stores.models.Store")
    
    # Indexes
    __table_args__ = (
        Index('idx_sync_jobs_store_status', 'store_id', 'status'),
        Index('idx_sync_jobs_entity_type', 'entity_type'),
        Index('idx_sync_jobs_celery_task', 'celery_task_id'),
        Index('idx_sync_jobs_created_at', 'created_at'),
    )

    def calculate_progress_percentage(self) -> float:
        """Calculate sync progress percentage."""
        if self.total_entities == 0:
            return 0.0
        return (self.processed_entities / self.total_entities) * 100

    def calculate_eta_seconds(self) -> float:
        """Calculate estimated time to completion in seconds."""
        if not self.started_at or self.processed_entities == 0:
            return 0.0
        
        elapsed = (datetime.utcnow() - self.started_at).total_seconds()
        rate = self.processed_entities / elapsed
        remaining = self.total_entities - self.processed_entities
        
        return remaining / rate if rate > 0 else 0.0


class WebhookEventLog(Base):
    """
    Enhanced webhook event logging with deduplication and retry tracking.
    Replaces existing WebhookEvent model with more robust features.
    """
    __tablename__ = "webhook_event_logs"

    id = Column(Integer, primary_key=True, index=True)
    
    # Webhook identification
    webhook_id = Column(String(255), nullable=True, index=True)  # Shopify webhook ID
    event_id = Column(String(255), nullable=False, index=True)  # Unique event identifier
    topic = Column(String(100), nullable=False)
    shop_domain = Column(String(255), nullable=False)
    
    # Event data
    payload = Column(JSON, nullable=False)
    headers = Column(JSON, nullable=True)  # Store relevant headers
    hmac_verified = Column(Boolean, default=False)
    
    # Processing status
    status = Column(String(20), default=WebhookEventStatus.PENDING.value)
    processing_attempts = Column(Integer, default=0)
    max_processing_attempts = Column(Integer, default=3)
    
    # Timing
    received_at = Column(DateTime(timezone=True), server_default=func.now())
    first_processed_at = Column(DateTime(timezone=True), nullable=True)
    last_processed_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    
    # Error tracking
    last_error_message = Column(Text, nullable=True)
    error_details = Column(JSON, nullable=True)
    
    # Deduplication
    content_hash = Column(String(64), nullable=False, index=True)  # SHA-256 of payload
    is_duplicate = Column(Boolean, default=False)
    original_event_id = Column(String(255), nullable=True)  # Reference to original if duplicate
    
    # Processing metadata
    processing_duration_ms = Column(Integer, nullable=True)
    celery_task_id = Column(String(255), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Indexes for performance and deduplication
    __table_args__ = (
        Index('idx_webhook_events_shop_topic', 'shop_domain', 'topic'),
        Index('idx_webhook_events_status', 'status'),
        Index('idx_webhook_events_content_hash', 'content_hash'),
        Index('idx_webhook_events_received_at', 'received_at'),
        Index('idx_webhook_events_event_id_unique', 'event_id', unique=True),
    )


class DeadLetterQueue(Base):
    """
    Dead letter queue for permanently failed jobs and events.
    Stores failed items for manual investigation and resolution.
    """
    __tablename__ = "dead_letter_queue"

    id = Column(Integer, primary_key=True, index=True)
    
    # Source identification
    source_type = Column(String(50), nullable=False)  # 'webhook', 'sync_job', 'api_call'
    source_id = Column(String(255), nullable=False)  # Original job/event ID
    entity_type = Column(String(50), nullable=True)  # products, orders, etc.
    store_id = Column(Integer, ForeignKey("stores.id"), nullable=True)
    
    # Failure details
    failure_reason = Column(String(100), nullable=False)  # 'max_retries_exceeded', 'permanent_error'
    error_message = Column(Text, nullable=False)
    error_details = Column(JSON, nullable=True)
    
    # Original data
    original_payload = Column(JSON, nullable=False)
    processing_history = Column(JSON, nullable=True)  # Array of retry attempts
    
    # Resolution tracking
    status = Column(String(20), default="pending")  # pending, investigating, resolved, discarded
    assigned_to = Column(String(255), nullable=True)  # Admin user handling the issue
    resolution_notes = Column(Text, nullable=True)
    resolved_at = Column(DateTime(timezone=True), nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    store = relationship("modules.stores.models.Store")
    
    # Indexes
    __table_args__ = (
        Index('idx_dlq_source_type_status', 'source_type', 'status'),
        Index('idx_dlq_store_entity', 'store_id', 'entity_type'),
        Index('idx_dlq_created_at', 'created_at'),
    )


class SyncMetrics(Base):
    """
    Aggregated metrics for sync performance monitoring and analytics.
    """
    __tablename__ = "sync_metrics"

    id = Column(Integer, primary_key=True, index=True)
    store_id = Column(Integer, ForeignKey("stores.id"), nullable=False)
    entity_type = Column(String(50), nullable=False)
    
    # Time period for metrics
    period_start = Column(DateTime(timezone=True), nullable=False)
    period_end = Column(DateTime(timezone=True), nullable=False)
    period_type = Column(String(20), nullable=False)  # 'hour', 'day', 'week', 'month'
    
    # Sync performance metrics
    total_sync_jobs = Column(Integer, default=0)
    successful_sync_jobs = Column(Integer, default=0)
    failed_sync_jobs = Column(Integer, default=0)
    total_entities_synced = Column(Integer, default=0)
    average_sync_duration_seconds = Column(Float, default=0.0)
    
    # API usage metrics
    total_api_calls = Column(Integer, default=0)
    total_cost_consumed = Column(Integer, default=0)
    rate_limit_hits = Column(Integer, default=0)
    
    # Webhook metrics
    total_webhooks_received = Column(Integer, default=0)
    successful_webhook_processing = Column(Integer, default=0)
    failed_webhook_processing = Column(Integer, default=0)
    duplicate_webhooks = Column(Integer, default=0)
    
    # Error metrics
    total_errors = Column(Integer, default=0)
    dead_letter_items = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    store = relationship("modules.stores.models.Store")
    
    # Indexes
    __table_args__ = (
        Index('idx_sync_metrics_store_period', 'store_id', 'period_start', 'period_end'),
        Index('idx_sync_metrics_entity_type', 'entity_type'),
    )
