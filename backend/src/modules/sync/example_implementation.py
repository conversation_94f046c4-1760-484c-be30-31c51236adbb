"""
Example implementation demonstrating the robust Shopify sync system.
Shows how to use all components together for production-ready sync.
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, Any

from fastapi import FastAPI, Request, HTTPException, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.sync.webhook_handler import webhook_handler
from modules.sync.sync_manager import SyncManager, SyncMode, RateLimitStrategy
from modules.sync.models import SyncEntityType
from modules.sync.retry_handler import retry_handler
from modules.sync.monitoring import sync_monitor
from modules.sync.scheduler import sync_scheduler
from modules.stores.models import Store

logger = logging.getLogger(__name__)

# Example FastAPI app setup
app = FastAPI(title="Shopify Sync System")


# ============================================================================
# WEBHOOK ENDPOINTS
# ============================================================================

@app.post("/webhooks/shopify/{shop_domain}")
async def handle_shopify_webhook(
    shop_domain: str,
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    Handle incoming Shopify webhooks with robust processing.
    
    This endpoint demonstrates:
    - HMAC verification
    - Deduplication
    - Async processing
    - Error handling
    """
    try:
        # Process webhook using the enhanced handler
        result = await webhook_handler.handle_webhook(
            request=request,
            db=db,
            shop_domain=shop_domain
        )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Webhook handling error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


# ============================================================================
# SYNC MANAGEMENT ENDPOINTS
# ============================================================================

@app.post("/sync/stores/{store_id}/entities/{entity_type}")
async def trigger_sync(
    store_id: int,
    entity_type: str,
    sync_mode: str = "incremental",
    db: AsyncSession = Depends(get_db)
):
    """
    Trigger manual sync for specific store and entity type.
    
    This endpoint demonstrates:
    - Manual sync triggering
    - Incremental vs full sync
    - Rate limiting
    - Progress tracking
    """
    try:
        # Get store
        store = await _get_store(db, store_id)
        if not store:
            raise HTTPException(status_code=404, detail="Store not found")
        
        # Validate entity type
        try:
            entity_enum = SyncEntityType(entity_type)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid entity type")
        
        # Validate sync mode
        try:
            mode_enum = SyncMode(sync_mode)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid sync mode")
        
        # Create sync manager with moderate rate limiting
        sync_manager = SyncManager(
            store=store,
            rate_limit_strategy=RateLimitStrategy.MODERATE
        )
        
        # Start sync
        sync_job = await sync_manager.sync_entity(
            db=db,
            entity_type=entity_enum,
            sync_mode=mode_enum,
            batch_size=50
        )
        
        return {
            "sync_job_id": sync_job.id,
            "status": sync_job.status,
            "entity_type": sync_job.entity_type,
            "sync_mode": sync_job.sync_mode,
            "created_at": sync_job.created_at.isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Sync trigger error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to trigger sync")


@app.get("/sync/stores/{store_id}/status")
async def get_sync_status(
    store_id: int,
    db: AsyncSession = Depends(get_db)
):
    """
    Get comprehensive sync status for a store.
    
    This endpoint demonstrates:
    - Sync progress monitoring
    - Checkpoint status
    - Recent sync history
    - Performance metrics
    """
    try:
        # Generate dashboard data
        dashboard_data = await sync_monitor.generate_dashboard_data(
            db=db,
            store_id=store_id,
            time_window_hours=24
        )
        
        return dashboard_data
        
    except Exception as e:
        logger.error(f"Status retrieval error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to get sync status")


# ============================================================================
# MONITORING ENDPOINTS
# ============================================================================

@app.get("/monitoring/metrics")
async def get_metrics(
    store_id: int = None,
    time_window_hours: int = 1,
    db: AsyncSession = Depends(get_db)
):
    """
    Get sync metrics for monitoring and alerting.
    
    This endpoint demonstrates:
    - Metrics collection
    - Alert generation
    - Performance monitoring
    """
    try:
        # Collect metrics
        metrics = await sync_monitor.collect_sync_metrics(
            db=db,
            store_id=store_id,
            time_window_hours=time_window_hours
        )
        
        # Check for alerts
        alerts = await sync_monitor.check_alerts(db, metrics)
        
        return {
            "metrics": [
                {
                    "name": metric.name,
                    "value": metric.value,
                    "type": metric.metric_type.value,
                    "labels": metric.labels,
                    "timestamp": metric.timestamp.isoformat()
                }
                for metric in metrics
            ],
            "alerts": [
                {
                    "name": alert.name,
                    "severity": alert.severity.value,
                    "message": alert.message,
                    "timestamp": alert.timestamp.isoformat()
                }
                for alert in alerts
            ]
        }
        
    except Exception as e:
        logger.error(f"Metrics retrieval error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to get metrics")


# ============================================================================
# SCHEDULED SYNC ENDPOINTS
# ============================================================================

@app.post("/sync/schedule/incremental")
async def schedule_incremental_syncs(
    store_id: int = None,
    db: AsyncSession = Depends(get_db)
):
    """
    Trigger scheduled incremental syncs.
    
    This endpoint demonstrates:
    - Scheduled sync execution
    - Backstop synchronization
    - Automatic reconciliation
    """
    try:
        result = await sync_scheduler.schedule_incremental_syncs(
            db=db,
            store_id=store_id
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Scheduled sync error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to schedule syncs")


@app.post("/sync/schedule/reconciliation")
async def schedule_reconciliation_syncs(
    store_id: int = None,
    db: AsyncSession = Depends(get_db)
):
    """
    Trigger scheduled reconciliation syncs.
    
    This endpoint demonstrates:
    - Full reconciliation
    - Data consistency checks
    - Missed webhook recovery
    """
    try:
        result = await sync_scheduler.schedule_reconciliation_syncs(
            db=db,
            store_id=store_id
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Reconciliation sync error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to schedule reconciliation")


# ============================================================================
# RETRY AND ERROR HANDLING ENDPOINTS
# ============================================================================

@app.post("/sync/retry/webhook/{webhook_event_id}")
async def retry_webhook(
    webhook_event_id: int,
    db: AsyncSession = Depends(get_db)
):
    """
    Manually retry failed webhook processing.
    
    This endpoint demonstrates:
    - Manual retry triggering
    - Error recovery
    - Dead letter queue management
    """
    try:
        success = await retry_handler.retry_webhook_processing(
            db=db,
            webhook_event_id=webhook_event_id
        )
        
        return {
            "webhook_event_id": webhook_event_id,
            "retry_scheduled": success,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Webhook retry error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to retry webhook")


@app.post("/sync/retry/job/{sync_job_id}")
async def retry_sync_job(
    sync_job_id: int,
    db: AsyncSession = Depends(get_db)
):
    """
    Manually retry failed sync job.
    
    This endpoint demonstrates:
    - Sync job retry
    - Checkpoint recovery
    - Error handling
    """
    try:
        success = await retry_handler.retry_sync_job(
            db=db,
            sync_job_id=sync_job_id
        )
        
        return {
            "sync_job_id": sync_job_id,
            "retry_scheduled": success,
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Sync job retry error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to retry sync job")


# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

async def _get_store(db: AsyncSession, store_id: int) -> Store:
    """Get store by ID."""
    from sqlalchemy import select
    
    result = await db.execute(
        select(Store).where(Store.id == store_id)
    )
    return result.scalar_one_or_none()


# ============================================================================
# EXAMPLE USAGE PATTERNS
# ============================================================================

async def example_complete_sync_workflow():
    """
    Example demonstrating a complete sync workflow.
    
    This shows how to:
    1. Set up a new store for sync
    2. Handle incoming webhooks
    3. Perform incremental sync
    4. Monitor performance
    5. Handle errors and retries
    """
    # This would typically be called from your application code
    pass


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
