"""
Monitoring and observability system for Shopify sync operations.
Provides metrics collection, alerting, and performance tracking.
"""

import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from enum import Enum
from dataclasses import dataclass

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_

from core.config import get_settings
from modules.sync.models import (
    SyncJob, SyncJobStatus, WebhookEventLog, WebhookEventStatus, 
    SyncMetrics, DeadLetterQueue, SyncCheckpoint
)

logger = logging.getLogger(__name__)
settings = get_settings()


class MetricType(str, Enum):
    """Types of metrics to collect."""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"


class AlertSeverity(str, Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class SyncMetric:
    """Sync performance metric."""
    name: str
    value: float
    metric_type: MetricType
    labels: Dict[str, str]
    timestamp: datetime


@dataclass
class Alert:
    """System alert."""
    name: str
    severity: AlertSeverity
    message: str
    labels: Dict[str, str]
    timestamp: datetime
    threshold_value: Optional[float] = None
    current_value: Optional[float] = None


class SyncMonitor:
    """
    Comprehensive monitoring system for Shopify sync operations.
    Collects metrics, generates alerts, and tracks performance.
    """

    def __init__(self):
        self.metrics_buffer: List[SyncMetric] = []
        self.alerts_buffer: List[Alert] = []
        
        # Alert thresholds
        self.alert_thresholds = {
            "webhook_failure_rate": 0.1,      # 10% failure rate
            "sync_job_failure_rate": 0.05,    # 5% failure rate
            "avg_webhook_processing_time": 5000,  # 5 seconds in ms
            "avg_sync_duration": 3600,        # 1 hour in seconds
            "dead_letter_queue_size": 100,    # 100 items
            "rate_limit_hits_per_hour": 50,   # 50 rate limit hits per hour
            "api_cost_per_hour": 50000,       # 50k cost points per hour
        }

    async def collect_sync_metrics(
        self,
        db: AsyncSession,
        store_id: Optional[int] = None,
        time_window_hours: int = 1
    ) -> List[SyncMetric]:
        """
        Collect comprehensive sync metrics for monitoring.
        
        Args:
            db: Database session
            store_id: Optional store ID filter
            time_window_hours: Time window for metrics collection
            
        Returns:
            List of collected metrics
        """
        cutoff_time = datetime.utcnow() - timedelta(hours=time_window_hours)
        metrics = []
        
        # Base filters
        store_filter = [SyncJob.store_id == store_id] if store_id else []
        webhook_store_filter = []
        if store_id:
            # For webhooks, we need to join with stores to filter by store_id
            # This is a simplified approach - in practice you'd want a proper join
            store_result = await db.execute(
                select(Store.shop_domain).where(Store.id == store_id)
            )
            shop_domain = store_result.scalar_one_or_none()
            if shop_domain:
                webhook_store_filter = [WebhookEventLog.shop_domain == shop_domain]
        
        # Sync job metrics
        sync_metrics = await self._collect_sync_job_metrics(
            db, cutoff_time, store_filter
        )
        metrics.extend(sync_metrics)
        
        # Webhook metrics
        webhook_metrics = await self._collect_webhook_metrics(
            db, cutoff_time, webhook_store_filter
        )
        metrics.extend(webhook_metrics)
        
        # Dead letter queue metrics
        dlq_metrics = await self._collect_dlq_metrics(
            db, cutoff_time, store_filter
        )
        metrics.extend(dlq_metrics)
        
        # API usage metrics
        api_metrics = await self._collect_api_usage_metrics(
            db, cutoff_time, store_filter
        )
        metrics.extend(api_metrics)
        
        # Store metrics in buffer
        self.metrics_buffer.extend(metrics)
        
        return metrics

    async def _collect_sync_job_metrics(
        self,
        db: AsyncSession,
        cutoff_time: datetime,
        store_filter: List
    ) -> List[SyncMetric]:
        """Collect sync job performance metrics."""
        metrics = []
        
        # Total sync jobs
        total_jobs_result = await db.execute(
            select(func.count(SyncJob.id))
            .where(and_(
                SyncJob.created_at >= cutoff_time,
                *store_filter
            ))
        )
        total_jobs = total_jobs_result.scalar() or 0
        
        metrics.append(SyncMetric(
            name="sync_jobs_total",
            value=total_jobs,
            metric_type=MetricType.COUNTER,
            labels={"time_window": "1h"},
            timestamp=datetime.utcnow()
        ))
        
        # Successful sync jobs
        successful_jobs_result = await db.execute(
            select(func.count(SyncJob.id))
            .where(and_(
                SyncJob.created_at >= cutoff_time,
                SyncJob.status == SyncJobStatus.COMPLETED.value,
                *store_filter
            ))
        )
        successful_jobs = successful_jobs_result.scalar() or 0
        
        metrics.append(SyncMetric(
            name="sync_jobs_successful",
            value=successful_jobs,
            metric_type=MetricType.COUNTER,
            labels={"time_window": "1h"},
            timestamp=datetime.utcnow()
        ))
        
        # Failed sync jobs
        failed_jobs_result = await db.execute(
            select(func.count(SyncJob.id))
            .where(and_(
                SyncJob.created_at >= cutoff_time,
                SyncJob.status == SyncJobStatus.FAILED.value,
                *store_filter
            ))
        )
        failed_jobs = failed_jobs_result.scalar() or 0
        
        metrics.append(SyncMetric(
            name="sync_jobs_failed",
            value=failed_jobs,
            metric_type=MetricType.COUNTER,
            labels={"time_window": "1h"},
            timestamp=datetime.utcnow()
        ))
        
        # Average sync duration
        avg_duration_result = await db.execute(
            select(func.avg(SyncJob.duration_seconds))
            .where(and_(
                SyncJob.completed_at >= cutoff_time,
                SyncJob.status == SyncJobStatus.COMPLETED.value,
                SyncJob.duration_seconds.isnot(None),
                *store_filter
            ))
        )
        avg_duration = avg_duration_result.scalar() or 0
        
        metrics.append(SyncMetric(
            name="sync_job_duration_avg_seconds",
            value=float(avg_duration),
            metric_type=MetricType.GAUGE,
            labels={"time_window": "1h"},
            timestamp=datetime.utcnow()
        ))
        
        # Sync job failure rate
        failure_rate = (failed_jobs / total_jobs * 100) if total_jobs > 0 else 0
        
        metrics.append(SyncMetric(
            name="sync_job_failure_rate_percent",
            value=failure_rate,
            metric_type=MetricType.GAUGE,
            labels={"time_window": "1h"},
            timestamp=datetime.utcnow()
        ))
        
        return metrics

    async def _collect_webhook_metrics(
        self,
        db: AsyncSession,
        cutoff_time: datetime,
        store_filter: List
    ) -> List[SyncMetric]:
        """Collect webhook processing metrics."""
        metrics = []
        
        # Total webhooks received
        total_webhooks_result = await db.execute(
            select(func.count(WebhookEventLog.id))
            .where(and_(
                WebhookEventLog.received_at >= cutoff_time,
                *store_filter
            ))
        )
        total_webhooks = total_webhooks_result.scalar() or 0
        
        metrics.append(SyncMetric(
            name="webhooks_received_total",
            value=total_webhooks,
            metric_type=MetricType.COUNTER,
            labels={"time_window": "1h"},
            timestamp=datetime.utcnow()
        ))
        
        # Successful webhook processing
        successful_webhooks_result = await db.execute(
            select(func.count(WebhookEventLog.id))
            .where(and_(
                WebhookEventLog.received_at >= cutoff_time,
                WebhookEventLog.status == WebhookEventStatus.COMPLETED.value,
                *store_filter
            ))
        )
        successful_webhooks = successful_webhooks_result.scalar() or 0
        
        metrics.append(SyncMetric(
            name="webhooks_processed_successful",
            value=successful_webhooks,
            metric_type=MetricType.COUNTER,
            labels={"time_window": "1h"},
            timestamp=datetime.utcnow()
        ))
        
        # Failed webhook processing
        failed_webhooks_result = await db.execute(
            select(func.count(WebhookEventLog.id))
            .where(and_(
                WebhookEventLog.received_at >= cutoff_time,
                WebhookEventLog.status == WebhookEventStatus.FAILED.value,
                *store_filter
            ))
        )
        failed_webhooks = failed_webhooks_result.scalar() or 0
        
        metrics.append(SyncMetric(
            name="webhooks_processed_failed",
            value=failed_webhooks,
            metric_type=MetricType.COUNTER,
            labels={"time_window": "1h"},
            timestamp=datetime.utcnow()
        ))
        
        # Duplicate webhooks
        duplicate_webhooks_result = await db.execute(
            select(func.count(WebhookEventLog.id))
            .where(and_(
                WebhookEventLog.received_at >= cutoff_time,
                WebhookEventLog.is_duplicate == True,
                *store_filter
            ))
        )
        duplicate_webhooks = duplicate_webhooks_result.scalar() or 0
        
        metrics.append(SyncMetric(
            name="webhooks_duplicate_total",
            value=duplicate_webhooks,
            metric_type=MetricType.COUNTER,
            labels={"time_window": "1h"},
            timestamp=datetime.utcnow()
        ))
        
        # Average webhook processing time
        avg_processing_time_result = await db.execute(
            select(func.avg(WebhookEventLog.processing_duration_ms))
            .where(and_(
                WebhookEventLog.completed_at >= cutoff_time,
                WebhookEventLog.status == WebhookEventStatus.COMPLETED.value,
                WebhookEventLog.processing_duration_ms.isnot(None),
                *store_filter
            ))
        )
        avg_processing_time = avg_processing_time_result.scalar() or 0
        
        metrics.append(SyncMetric(
            name="webhook_processing_time_avg_ms",
            value=float(avg_processing_time),
            metric_type=MetricType.GAUGE,
            labels={"time_window": "1h"},
            timestamp=datetime.utcnow()
        ))
        
        # Webhook failure rate
        failure_rate = (failed_webhooks / total_webhooks * 100) if total_webhooks > 0 else 0
        
        metrics.append(SyncMetric(
            name="webhook_failure_rate_percent",
            value=failure_rate,
            metric_type=MetricType.GAUGE,
            labels={"time_window": "1h"},
            timestamp=datetime.utcnow()
        ))
        
        return metrics

    async def _collect_dlq_metrics(
        self,
        db: AsyncSession,
        cutoff_time: datetime,
        store_filter: List
    ) -> List[SyncMetric]:
        """Collect dead letter queue metrics."""
        metrics = []
        
        # Total DLQ items
        total_dlq_result = await db.execute(
            select(func.count(DeadLetterQueue.id))
            .where(and_(
                DeadLetterQueue.created_at >= cutoff_time,
                *store_filter
            ))
        )
        total_dlq = total_dlq_result.scalar() or 0
        
        metrics.append(SyncMetric(
            name="dead_letter_queue_items_total",
            value=total_dlq,
            metric_type=MetricType.GAUGE,
            labels={"time_window": "1h"},
            timestamp=datetime.utcnow()
        ))
        
        # Pending DLQ items
        pending_dlq_result = await db.execute(
            select(func.count(DeadLetterQueue.id))
            .where(and_(
                DeadLetterQueue.status == "pending",
                *store_filter
            ))
        )
        pending_dlq = pending_dlq_result.scalar() or 0
        
        metrics.append(SyncMetric(
            name="dead_letter_queue_items_pending",
            value=pending_dlq,
            metric_type=MetricType.GAUGE,
            labels={},
            timestamp=datetime.utcnow()
        ))
        
        return metrics

    async def _collect_api_usage_metrics(
        self,
        db: AsyncSession,
        cutoff_time: datetime,
        store_filter: List
    ) -> List[SyncMetric]:
        """Collect API usage metrics."""
        metrics = []
        
        # Total API calls
        total_api_calls_result = await db.execute(
            select(func.sum(SyncJob.api_calls_made))
            .where(and_(
                SyncJob.created_at >= cutoff_time,
                *store_filter
            ))
        )
        total_api_calls = total_api_calls_result.scalar() or 0
        
        metrics.append(SyncMetric(
            name="api_calls_total",
            value=total_api_calls,
            metric_type=MetricType.COUNTER,
            labels={"time_window": "1h"},
            timestamp=datetime.utcnow()
        ))
        
        # Total cost consumed
        total_cost_result = await db.execute(
            select(func.sum(SyncJob.cost_consumed))
            .where(and_(
                SyncJob.created_at >= cutoff_time,
                *store_filter
            ))
        )
        total_cost = total_cost_result.scalar() or 0
        
        metrics.append(SyncMetric(
            name="api_cost_consumed_total",
            value=total_cost,
            metric_type=MetricType.COUNTER,
            labels={"time_window": "1h"},
            timestamp=datetime.utcnow()
        ))
        
        # Rate limit hits
        rate_limit_hits_result = await db.execute(
            select(func.sum(SyncJob.rate_limit_hits))
            .where(and_(
                SyncJob.created_at >= cutoff_time,
                *store_filter
            ))
        )
        rate_limit_hits = rate_limit_hits_result.scalar() or 0
        
        metrics.append(SyncMetric(
            name="rate_limit_hits_total",
            value=rate_limit_hits,
            metric_type=MetricType.COUNTER,
            labels={"time_window": "1h"},
            timestamp=datetime.utcnow()
        ))
        
        return metrics

    async def check_alerts(
        self,
        db: AsyncSession,
        metrics: List[SyncMetric]
    ) -> List[Alert]:
        """
        Check metrics against thresholds and generate alerts.

        Args:
            db: Database session
            metrics: List of collected metrics

        Returns:
            List of generated alerts
        """
        alerts = []

        # Convert metrics to dict for easy lookup
        metrics_dict = {metric.name: metric.value for metric in metrics}

        # Check webhook failure rate
        webhook_failure_rate = metrics_dict.get("webhook_failure_rate_percent", 0)
        if webhook_failure_rate > self.alert_thresholds["webhook_failure_rate"] * 100:
            alerts.append(Alert(
                name="high_webhook_failure_rate",
                severity=AlertSeverity.WARNING,
                message=f"Webhook failure rate is {webhook_failure_rate:.1f}%",
                labels={"metric": "webhook_failure_rate"},
                timestamp=datetime.utcnow(),
                threshold_value=self.alert_thresholds["webhook_failure_rate"] * 100,
                current_value=webhook_failure_rate
            ))

        # Check sync job failure rate
        sync_failure_rate = metrics_dict.get("sync_job_failure_rate_percent", 0)
        if sync_failure_rate > self.alert_thresholds["sync_job_failure_rate"] * 100:
            alerts.append(Alert(
                name="high_sync_job_failure_rate",
                severity=AlertSeverity.ERROR,
                message=f"Sync job failure rate is {sync_failure_rate:.1f}%",
                labels={"metric": "sync_job_failure_rate"},
                timestamp=datetime.utcnow(),
                threshold_value=self.alert_thresholds["sync_job_failure_rate"] * 100,
                current_value=sync_failure_rate
            ))

        # Check dead letter queue size
        dlq_size = metrics_dict.get("dead_letter_queue_items_pending", 0)
        if dlq_size > self.alert_thresholds["dead_letter_queue_size"]:
            alerts.append(Alert(
                name="large_dead_letter_queue",
                severity=AlertSeverity.ERROR,
                message=f"Dead letter queue has {dlq_size} pending items",
                labels={"metric": "dead_letter_queue_size"},
                timestamp=datetime.utcnow(),
                threshold_value=self.alert_thresholds["dead_letter_queue_size"],
                current_value=dlq_size
            ))

        # Store alerts in buffer
        self.alerts_buffer.extend(alerts)

        return alerts


# Global monitor instance
sync_monitor = SyncMonitor()
