#!/bin/bash
set -e

# Create multiple databases for the application
# This script is run by PostgreSQL during container initialization

psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    -- Create staging database for Airbyte destination
    CREATE DATABASE airbyte_staging;
    GRANT ALL PRIVILEGES ON DATABASE airbyte_staging TO $POSTGRES_USER;
    
    -- Create test database
    CREATE DATABASE ecommerce_test;
    GRANT ALL PRIVILEGES ON DATABASE ecommerce_test TO $POSTGRES_USER;
    
    -- Create extensions in main database
    \c $POSTGRES_DB;
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    CREATE EXTENSION IF NOT EXISTS "pg_trgm";
    CREATE EXTENSION IF NOT EXISTS "btree_gin";
    
    -- Create extensions in staging database
    \c airbyte_staging;
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    CREATE EXTENSION IF NOT EXISTS "pg_trgm";
    CREATE EXTENSION IF NOT EXISTS "btree_gin";
    
    -- Create extensions in test database
    \c ecommerce_test;
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    CREATE EXTENSION IF NOT EXISTS "pg_trgm";
    CREATE EXTENSION IF NOT EXISTS "btree_gin";
EOSQL

echo "Multiple databases created successfully!"
