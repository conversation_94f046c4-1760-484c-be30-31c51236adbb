#!/usr/bin/env python3
"""
Airbyte Connection Setup Script

This script automates the creation of Airbyte sources, destinations, and connections
for Shopify sync using the configuration templates.

Usage:
    python setup_connections.py --shop-domain your-shop.myshopify.com --access-token your-token
"""

import json
import os
import sys
import argparse
import requests
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class AirbyteSetup:
    """Handles Airbyte workspace, source, destination, and connection setup."""
    
    def __init__(self, api_url: str, username: str = "airbyte", password: str = "password"):
        self.api_url = api_url.rstrip('/')
        self.username = username
        self.password = password
        self.session = requests.Session()
        self.session.auth = (username, password)
        self.workspace_id = None
        
    def wait_for_airbyte(self, max_retries: int = 30, delay: int = 10) -> bool:
        """Wait for Airbyte server to be ready."""
        logger.info("Waiting for Airbyte server to be ready...")
        
        for attempt in range(max_retries):
            try:
                response = self.session.get(f"{self.api_url}/health", timeout=5)
                if response.status_code == 200:
                    logger.info("Airbyte server is ready!")
                    return True
            except requests.exceptions.RequestException as e:
                logger.debug(f"Attempt {attempt + 1}: {e}")
                
            if attempt < max_retries - 1:
                logger.info(f"Waiting {delay} seconds before retry...")
                time.sleep(delay)
                
        logger.error("Airbyte server is not ready after maximum retries")
        return False
        
    def create_workspace(self, name: str = "Shopify E-commerce Sync") -> str:
        """Create or get existing workspace."""
        # First, try to list existing workspaces
        try:
            response = self.session.post(f"{self.api_url}/workspaces/list")
            if response.status_code == 200:
                workspaces = response.json().get("workspaces", [])
                for workspace in workspaces:
                    if workspace["name"] == name:
                        self.workspace_id = workspace["workspaceId"]
                        logger.info(f"Using existing workspace: {self.workspace_id}")
                        return self.workspace_id
        except Exception as e:
            logger.warning(f"Could not list workspaces: {e}")
            
        # Create new workspace
        workspace_data = {
            "name": name,
            "slug": "shopify-ecommerce-sync",
            "initialSetupComplete": True,
            "displaySetupWizard": False,
            "anonymousDataCollection": False,
            "news": False,
            "securityUpdates": True
        }
        
        try:
            response = self.session.post(f"{self.api_url}/workspaces/create", json=workspace_data)
            if response.status_code == 200:
                self.workspace_id = response.json()["workspaceId"]
                logger.info(f"Created new workspace: {self.workspace_id}")
                return self.workspace_id
            else:
                logger.error(f"Failed to create workspace: {response.text}")
                return None
        except Exception as e:
            logger.error(f"Error creating workspace: {e}")
            return None
            
    def create_source(self, shop_domain: str, access_token: str, start_date: str = None) -> str:
        """Create Shopify source."""
        if not start_date:
            # Default to 30 days ago
            start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
            
        # Load template
        template_path = os.path.join(os.path.dirname(__file__), "configs", "shopify_source_template.json")
        with open(template_path, 'r') as f:
            source_config = json.load(f)
            
        # Replace placeholders
        source_config["workspaceId"] = self.workspace_id
        source_config["connectionConfiguration"]["shop"] = shop_domain
        source_config["connectionConfiguration"]["credentials"]["api_password"] = access_token
        source_config["connectionConfiguration"]["start_date"] = start_date
        source_config["name"] = f"Shopify Source - {shop_domain}"
        
        try:
            response = self.session.post(f"{self.api_url}/sources/create", json=source_config)
            if response.status_code == 200:
                source_id = response.json()["sourceId"]
                logger.info(f"Created Shopify source: {source_id}")
                return source_id
            else:
                logger.error(f"Failed to create source: {response.text}")
                return None
        except Exception as e:
            logger.error(f"Error creating source: {e}")
            return None
            
    def create_destination(self, shop_domain: str, postgres_password: str) -> str:
        """Create PostgreSQL destination."""
        # Load template
        template_path = os.path.join(os.path.dirname(__file__), "configs", "postgres_destination_template.json")
        with open(template_path, 'r') as f:
            dest_config = json.load(f)
            
        # Replace placeholders
        dest_config["workspaceId"] = self.workspace_id
        dest_config["connectionConfiguration"]["password"] = postgres_password
        dest_config["name"] = f"PostgreSQL Destination - {shop_domain}"
        
        try:
            response = self.session.post(f"{self.api_url}/destinations/create", json=dest_config)
            if response.status_code == 200:
                dest_id = response.json()["destinationId"]
                logger.info(f"Created PostgreSQL destination: {dest_id}")
                return dest_id
            else:
                logger.error(f"Failed to create destination: {response.text}")
                return None
        except Exception as e:
            logger.error(f"Error creating destination: {e}")
            return None
            
    def discover_source_schema(self, source_id: str) -> Dict[str, Any]:
        """Discover source schema."""
        try:
            response = self.session.post(f"{self.api_url}/sources/discover_schema", json={"sourceId": source_id})
            if response.status_code == 200:
                catalog = response.json()["catalog"]
                logger.info("Successfully discovered source schema")
                return catalog
            else:
                logger.error(f"Failed to discover schema: {response.text}")
                return None
        except Exception as e:
            logger.error(f"Error discovering schema: {e}")
            return None
            
    def create_connection(self, source_id: str, destination_id: str, shop_domain: str, catalog: Dict[str, Any]) -> str:
        """Create connection between source and destination."""
        # Load template
        template_path = os.path.join(os.path.dirname(__file__), "configs", "connection_template.json")
        with open(template_path, 'r') as f:
            conn_config = json.load(f)
            
        # Replace placeholders
        conn_config["name"] = f"Shopify to PostgreSQL - {shop_domain}"
        conn_config["sourceId"] = source_id
        conn_config["destinationId"] = destination_id
        conn_config["syncCatalog"] = catalog
        
        try:
            response = self.session.post(f"{self.api_url}/connections/create", json=conn_config)
            if response.status_code == 200:
                conn_id = response.json()["connectionId"]
                logger.info(f"Created connection: {conn_id}")
                return conn_id
            else:
                logger.error(f"Failed to create connection: {response.text}")
                return None
        except Exception as e:
            logger.error(f"Error creating connection: {e}")
            return None
            
    def trigger_sync(self, connection_id: str) -> str:
        """Trigger initial sync."""
        try:
            response = self.session.post(f"{self.api_url}/connections/sync", json={"connectionId": connection_id})
            if response.status_code == 200:
                job_id = response.json()["job"]["id"]
                logger.info(f"Triggered initial sync, job ID: {job_id}")
                return job_id
            else:
                logger.error(f"Failed to trigger sync: {response.text}")
                return None
        except Exception as e:
            logger.error(f"Error triggering sync: {e}")
            return None


def main():
    parser = argparse.ArgumentParser(description="Set up Airbyte connections for Shopify sync")
    parser.add_argument("--shop-domain", required=True, help="Shopify shop domain (e.g., your-shop.myshopify.com)")
    parser.add_argument("--access-token", required=True, help="Shopify Admin API access token")
    parser.add_argument("--start-date", help="Sync start date (YYYY-MM-DD), defaults to 30 days ago")
    parser.add_argument("--api-url", default="http://localhost:8001/api/v1", help="Airbyte API URL")
    parser.add_argument("--postgres-password", default="dev_password", help="PostgreSQL password")
    parser.add_argument("--trigger-sync", action="store_true", help="Trigger initial sync after setup")
    
    args = parser.parse_args()
    
    # Initialize Airbyte setup
    setup = AirbyteSetup(args.api_url)
    
    # Wait for Airbyte to be ready
    if not setup.wait_for_airbyte():
        sys.exit(1)
        
    # Create workspace
    workspace_id = setup.create_workspace()
    if not workspace_id:
        sys.exit(1)
        
    # Create source
    source_id = setup.create_source(args.shop_domain, args.access_token, args.start_date)
    if not source_id:
        sys.exit(1)
        
    # Create destination
    dest_id = setup.create_destination(args.shop_domain, args.postgres_password)
    if not dest_id:
        sys.exit(1)
        
    # Discover schema
    catalog = setup.discover_source_schema(source_id)
    if not catalog:
        sys.exit(1)
        
    # Create connection
    conn_id = setup.create_connection(source_id, dest_id, args.shop_domain, catalog)
    if not conn_id:
        sys.exit(1)
        
    # Trigger initial sync if requested
    if args.trigger_sync:
        job_id = setup.trigger_sync(conn_id)
        if job_id:
            logger.info(f"Setup complete! Monitor sync progress in Airbyte UI: http://localhost:8080")
        else:
            logger.warning("Setup complete but initial sync failed to start")
    else:
        logger.info("Setup complete! You can now trigger syncs manually or via the orchestration worker")
        
    # Output connection details
    print(f"\n=== Airbyte Setup Complete ===")
    print(f"Workspace ID: {workspace_id}")
    print(f"Source ID: {source_id}")
    print(f"Destination ID: {dest_id}")
    print(f"Connection ID: {conn_id}")
    print(f"Airbyte UI: http://localhost:8080")
    print("===============================\n")


if __name__ == "__main__":
    main()
