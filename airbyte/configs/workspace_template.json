{"name": "Shopify E-commerce Sync", "slug": "shopify-ecommerce-sync", "initialSetupComplete": true, "displaySetupWizard": false, "anonymousDataCollection": false, "news": false, "securityUpdates": true, "notifications": [{"notificationType": "slack", "sendOnSuccess": false, "sendOnFailure": true, "slackConfiguration": {"webhook": "${SLACK_WEBHOOK_URL}"}}], "webhookConfigs": [{"name": "Sync Status Webhook", "authToken": {"authType": "API_KEY", "headerKey": "Authorization", "headerValue": "Bearer ${WEBHOOK_AUTH_TOKEN}"}, "validationUrl": "${WEBHOOK_VALIDATION_URL}", "executionUrl": "${WEBHOOK_EXECUTION_URL}", "executionBody": "{\"connectionId\": \"${connectionId}\", \"jobId\": \"${jobId}\", \"status\": \"${jobStatus}\", \"startTime\": \"${jobStartTime}\", \"endTime\": \"${jobEndTime}\"}", "validationBody": "{\"test\": true}"}]}