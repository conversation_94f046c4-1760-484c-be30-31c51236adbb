{"destinationDefinitionId": "25c5221d-dce2-4163-ade9-739ef790f503", "workspaceId": "${AIRBYTE_WORKSPACE_ID}", "connectionConfiguration": {"host": "db", "port": 5432, "database": "ecommerce_db", "schema": "airbyte_staging", "username": "app_user", "password": "${POSTGRES_PASSWORD}", "ssl_mode": {"mode": "disable"}, "tunnel_method": {"tunnel_method": "NO_TUNNEL"}, "raw_data_schema": "airbyte_raw", "disable_type_dedupe": false, "drop_cascade": false}, "name": "PostgreSQL Destination - ${SHOP_NAME}", "destinationName": "Postgres"}