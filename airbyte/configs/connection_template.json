{"name": "Shopify to PostgreSQL - ${SHOP_NAME}", "namespaceDefinition": "destination", "namespaceFormat": "${SOURCE_NAMESPACE}", "prefix": "shopify_", "sourceId": "${SOURCE_ID}", "destinationId": "${DESTINATION_ID}", "operationIds": [], "syncCatalog": {"streams": [{"stream": {"name": "products", "jsonSchema": {}, "supportedSyncModes": ["full_refresh", "incremental"], "sourceDefinedCursor": true, "defaultCursorField": ["updated_at"], "sourceDefinedPrimaryKey": [["id"]], "namespace": "shopify"}, "config": {"syncMode": "incremental", "cursorField": ["updated_at"], "destinationSyncMode": "append_dedup", "primaryKey": [["id"]], "aliasName": "products", "selected": true, "fieldSelectionEnabled": false}}, {"stream": {"name": "orders", "jsonSchema": {}, "supportedSyncModes": ["full_refresh", "incremental"], "sourceDefinedCursor": true, "defaultCursorField": ["updated_at"], "sourceDefinedPrimaryKey": [["id"]], "namespace": "shopify"}, "config": {"syncMode": "incremental", "cursorField": ["updated_at"], "destinationSyncMode": "append_dedup", "primaryKey": [["id"]], "aliasName": "orders", "selected": true, "fieldSelectionEnabled": false}}, {"stream": {"name": "customers", "jsonSchema": {}, "supportedSyncModes": ["full_refresh", "incremental"], "sourceDefinedCursor": true, "defaultCursorField": ["updated_at"], "sourceDefinedPrimaryKey": [["id"]], "namespace": "shopify"}, "config": {"syncMode": "incremental", "cursorField": ["updated_at"], "destinationSyncMode": "append_dedup", "primaryKey": [["id"]], "aliasName": "customers", "selected": true, "fieldSelectionEnabled": false}}, {"stream": {"name": "order_refunds", "jsonSchema": {}, "supportedSyncModes": ["full_refresh", "incremental"], "sourceDefinedCursor": true, "defaultCursorField": ["created_at"], "sourceDefinedPrimaryKey": [["id"]], "namespace": "shopify"}, "config": {"syncMode": "incremental", "cursorField": ["created_at"], "destinationSyncMode": "append_dedup", "primaryKey": [["id"]], "aliasName": "order_refunds", "selected": true, "fieldSelectionEnabled": false}}, {"stream": {"name": "transactions", "jsonSchema": {}, "supportedSyncModes": ["full_refresh", "incremental"], "sourceDefinedCursor": true, "defaultCursorField": ["created_at"], "sourceDefinedPrimaryKey": [["id"]], "namespace": "shopify"}, "config": {"syncMode": "incremental", "cursorField": ["created_at"], "destinationSyncMode": "append_dedup", "primaryKey": [["id"]], "aliasName": "transactions", "selected": true, "fieldSelectionEnabled": false}}, {"stream": {"name": "inventory_levels", "jsonSchema": {}, "supportedSyncModes": ["full_refresh"], "sourceDefinedCursor": false, "sourceDefinedPrimaryKey": [["inventory_item_id", "location_id"]], "namespace": "shopify"}, "config": {"syncMode": "full_refresh", "destinationSyncMode": "overwrite", "primaryKey": [["inventory_item_id"], ["location_id"]], "aliasName": "inventory_levels", "selected": true, "fieldSelectionEnabled": false}}]}, "schedule": {"units": 1, "timeUnit": "hours"}, "status": "active", "resourceRequirements": {"cpu_request": "0.25", "cpu_limit": "0.5", "memory_request": "512Mi", "memory_limit": "1Gi"}, "sourceCatalogId": "${SOURCE_CATALOG_ID}"}